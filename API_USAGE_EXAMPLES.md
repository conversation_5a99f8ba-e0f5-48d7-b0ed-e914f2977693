# Permission Management API - GET Endpoints Usage Examples

This document provides comprehensive examples of how to use the new GET endpoints for `/users` and `/roles/list` with query parameters for filtering, pagination, and sorting.

## Overview

The API has been refactored to support RESTful GET requests with query parameters while maintaining backward compatibility with the original POST endpoints.

### Available Endpoints

- **GET** `/api/v1/users` - New RESTful endpoint
- **GET** `/api/v1/roles/list` - New RESTful endpoint
- **POST** `/api/v1/users` - Deprecated (backward compatibility)
- **POST** `/api/v1/roles/list` - Deprecated (backward compatibility)

## GetUsers API Examples

### Basic Request
```http
GET /api/v1/users
Authorization: Bearer <your-jwt-token>
```

### Pagination
```http
GET /api/v1/users?offset=0&limit=10
Authorization: Bearer <your-jwt-token>
```

### Search by Name or Email
```http
GET /api/v1/users?searchKey=john
Authorization: Bearer <your-jwt-token>
```

### Filtering Examples

#### Filter by Status (Active Users)
```http
GET /api/v1/users?filter[0][column]=status&filter[0][value][0]=1
Authorization: Bearer <your-jwt-token>
```

#### Filter by Multiple Statuses
```http
GET /api/v1/users?filter[0][column]=status&filter[0][value][0]=1&filter[0][value][1]=0
Authorization: Bearer <your-jwt-token>
```

#### Filter by Email Domain
```http
GET /api/v1/users?filter[0][column]=email&filter[0][value][0]=@superbank.id
Authorization: Bearer <your-jwt-token>
```

#### Filter by Creation Date Range
```http
GET /api/v1/users?filter[0][column]=created_at&filter[0][value][0]=2024-01-01&filter[0][value][1]=2024-12-31
Authorization: Bearer <your-jwt-token>
```

### Sorting Examples

#### Sort by Name (Ascending)
```http
GET /api/v1/users?sortBy[column]=name&sortBy[sort]=ASC
Authorization: Bearer <your-jwt-token>
```

#### Sort by Creation Date (Descending)
```http
GET /api/v1/users?sortBy[column]=created_at&sortBy[sort]=DESC
Authorization: Bearer <your-jwt-token>
```

### Complex Query Example
```http
GET /api/v1/users?searchKey=admin&offset=0&limit=20&filter[0][column]=status&filter[0][value][0]=1&sortBy[column]=created_at&sortBy[sort]=DESC
Authorization: Bearer <your-jwt-token>
```

## GetListRole API Examples

### Basic Request
```http
GET /api/v1/roles/list
Authorization: Bearer <your-jwt-token>
```

### Pagination
```http
GET /api/v1/roles/list?offset=0&limit=5
Authorization: Bearer <your-jwt-token>
```

### Search by Role Name
```http
GET /api/v1/roles/list?searchKey=admin
Authorization: Bearer <your-jwt-token>
```

### Filtering Examples

#### Filter by Status (Active Roles)
```http
GET /api/v1/roles/list?filter[0][column]=status&filter[0][value][0]=1
Authorization: Bearer <your-jwt-token>
```

#### Filter by Role Name Pattern
```http
GET /api/v1/roles/list?filter[0][column]=name&filter[0][value][0]=Manager
Authorization: Bearer <your-jwt-token>
```

#### Filter by Creation Date
```http
GET /api/v1/roles/list?filter[0][column]=created_at&filter[0][value][0]=2024-01-01
Authorization: Bearer <your-jwt-token>
```

### Sorting Examples

#### Sort by Name (Ascending)
```http
GET /api/v1/roles/list?sortBy[column]=name&sortBy[sort]=ASC
Authorization: Bearer <your-jwt-token>
```

#### Sort by Status then Name
```http
GET /api/v1/roles/list?sortBy[column]=status&sortBy[sort]=DESC
Authorization: Bearer <your-jwt-token>
```

### Complex Query Example
```http
GET /api/v1/roles/list?searchKey=banking&offset=0&limit=10&filter[0][column]=status&filter[0][value][0]=1&sortBy[column]=name&sortBy[sort]=ASC
Authorization: Bearer <your-jwt-token>
```

## Query Parameter Structure

### Filter Parameter Structure
```
filter[index][column] = column_name
filter[index][value][value_index] = filter_value
```

### Sort Parameter Structure
```
sortBy[column] = column_name
sortBy[sort] = ASC|DESC
```

## Available Filter Columns

### For Users (`/api/v1/users`)
- `name` - User's full name
- `email` - User's email address
- `status` - User status (0=inactive, 1=active)
- `created_at` - Creation timestamp
- `updated_at` - Last update timestamp
- `created_by` - Creator identifier
- `updated_by` - Last updater identifier

### For Roles (`/api/v1/roles/list`)
- `name` - Role name
- `status` - Role status (0=inactive, 1=active)
- `created_at` - Creation timestamp
- `updated_at` - Last update timestamp
- `created_by` - Creator identifier
- `updated_by` - Last updater identifier

## Response Format

Both endpoints return the same response structure as before:

### Users Response
```json
{
  "count": 25,
  "offset": 0,
  "data": [
    {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>",
      "createdAt": "2024-01-15T10:30:00Z",
      "updatedAt": "2024-01-20T14:45:00Z",
      "createdBy": "admin",
      "updatedBy": "admin",
      "status": 1,
      "roles": ["Admin", "User Manager"],
      "userID": "user-123"
    }
  ]
}
```

### Roles Response
```json
{
  "count": 5,
  "offset": 0,
  "data": [
    {
      "id": 1,
      "name": "Administrator",
      "status": 1,
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-01T00:00:00Z",
      "createdBy": "system",
      "updatedBy": "system"
    }
  ]
}
```

## JavaScript/Frontend Examples

### Using Fetch API
```javascript
// Get active users with pagination
const getActiveUsers = async () => {
  const params = new URLSearchParams({
    'offset': '0',
    'limit': '20',
    'filter[0][column]': 'status',
    'filter[0][value][0]': '1',
    'sortBy[column]': 'name',
    'sortBy[sort]': 'ASC'
  });
  
  const response = await fetch(`/api/v1/users?${params}`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  
  return response.json();
};

// Get roles by name search
const searchRoles = async (searchTerm) => {
  const params = new URLSearchParams({
    'searchKey': searchTerm,
    'filter[0][column]': 'status',
    'filter[0][value][0]': '1'
  });
  
  const response = await fetch(`/api/v1/roles/list?${params}`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  
  return response.json();
};
```

### Using Axios
```javascript
// Get users with multiple filters
const getUsersWithFilters = async () => {
  const response = await axios.get('/api/v1/users', {
    params: {
      'searchKey': 'admin',
      'offset': 0,
      'limit': 10,
      'filter[0][column]': 'status',
      'filter[0][value][0]': 1,
      'filter[1][column]': 'email',
      'filter[1][value][0]': '@superbank.id',
      'sortBy[column]': 'created_at',
      'sortBy[sort]': 'DESC'
    },
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  
  return response.data;
};
```

## Migration Notes

- The new GET endpoints accept the same request parameters as the original POST endpoints
- Query parameters are automatically mapped to the request structure by the framework
- All existing validation rules and business logic remain unchanged
- The deprecated POST endpoints will continue to work for backward compatibility
- Consider updating client applications to use the new GET endpoints for better RESTful compliance

## Error Responses

Error responses remain the same format:
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid filter parameter"
  }
}
```

Common HTTP status codes:
- `200` - Success
- `400` - Bad Request (invalid parameters)
- `401` - Unauthorized (invalid/missing token)
- `403` - Forbidden (insufficient permissions)
- `500` - Internal Server Error
