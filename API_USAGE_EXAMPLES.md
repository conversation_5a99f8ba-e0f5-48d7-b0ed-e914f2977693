# Permission Management API - GET Endpoints Usage Examples

This document provides comprehensive examples of how to use the new GET endpoints for `/users` and `/roles/list` with query parameters for filtering, pagination, and sorting.

## Overview

The API has been refactored to support RESTful GET requests with flat query parameters while maintaining backward compatibility with the original POST endpoints.

### Available Endpoints

- **GET** `/api/v1/users` - New RESTful endpoint with flat filters
- **GET** `/api/v1/roles/list` - New RESTful endpoint with flat filters
- **POST** `/api/v1/users` - Deprecated (backward compatibility)
- **POST** `/api/v1/roles/list` - Deprecated (backward compatibility)

## GetUsers API Examples

### Basic Request
```http
GET /api/v1/users
Authorization: Bearer <your-jwt-token>
```

### Pagination
```http
GET /api/v1/users?offset=0&limit=10
Authorization: Bearer <your-jwt-token>
```

### Search by Name or Email
```http
GET /api/v1/users?searchKey=john
Authorization: Bearer <your-jwt-token>
```

### Filtering Examples

#### Filter by Status (Active Users)
```http
GET /api/v1/users?status=1
Authorization: Bearer <your-jwt-token>
```

#### Filter by Multiple Statuses
```http
GET /api/v1/users?status=1&status=0
Authorization: Bearer <your-jwt-token>
```

#### Filter by Role ID
```http
GET /api/v1/users?roleId=1
Authorization: Bearer <your-jwt-token>
```

#### Filter by Multiple Role IDs
```http
GET /api/v1/users?roleId=1&roleId=2&roleId=3
Authorization: Bearer <your-jwt-token>
```

### Sorting Examples

#### Sort by Name (Ascending)
```http
GET /api/v1/users?sortBy[column]=name&sortBy[sort]=ASC
Authorization: Bearer <your-jwt-token>
```

#### Sort by Creation Date (Descending)
```http
GET /api/v1/users?sortBy[column]=created_at&sortBy[sort]=DESC
Authorization: Bearer <your-jwt-token>
```

### Complex Query Example
```http
GET /api/v1/users?searchKey=admin&offset=0&limit=20&status=1&roleId=1&roleId=2&sortBy[column]=created_at&sortBy[sort]=DESC
Authorization: Bearer <your-jwt-token>
```

## GetListRole API Examples

### Basic Request
```http
GET /api/v1/roles/list
Authorization: Bearer <your-jwt-token>
```

### Pagination
```http
GET /api/v1/roles/list?offset=0&limit=5
Authorization: Bearer <your-jwt-token>
```

### Search by Role Name
```http
GET /api/v1/roles/list?searchKey=admin
Authorization: Bearer <your-jwt-token>
```

### Filtering Examples

#### Filter by Status (Active Roles)
```http
GET /api/v1/roles/list?status=1
Authorization: Bearer <your-jwt-token>
```

#### Filter by Multiple Statuses
```http
GET /api/v1/roles/list?status=1&status=0
Authorization: Bearer <your-jwt-token>
```

### Sorting Examples

#### Sort by Name (Ascending)
```http
GET /api/v1/roles/list?sortBy[column]=name&sortBy[sort]=ASC
Authorization: Bearer <your-jwt-token>
```

#### Sort by Status then Name
```http
GET /api/v1/roles/list?sortBy[column]=status&sortBy[sort]=DESC
Authorization: Bearer <your-jwt-token>
```

### Complex Query Example
```http
GET /api/v1/roles/list?searchKey=banking&offset=0&limit=10&status=1&sortBy[column]=name&sortBy[sort]=ASC
Authorization: Bearer <your-jwt-token>
```

## Query Parameter Structure

### Filter Parameters (Flat Structure)
```
status = 1                    # Single status filter
status = 1&status = 0         # Multiple status values
roleId = 1                    # Single role ID filter (users only)
roleId = 1&roleId = 2         # Multiple role ID values (users only)
```

### Sort Parameter Structure
```
sortBy[column] = column_name
sortBy[sort] = ASC|DESC
```

## Available Filter Parameters

### For Users (`/api/v1/users`)
- `status` - User status (0=inactive, 1=active) - accepts multiple values
- `roleId` - Role ID for filtering users by role - accepts multiple values
- `searchKey` - Search in name and email fields
- `offset` - Pagination offset
- `limit` - Number of results per page
- `sortBy[column]` - Sort column (id, name, created_at, updated_at, status, email)
- `sortBy[sort]` - Sort direction (ASC, DESC)

### For Roles (`/api/v1/roles/list`)
- `status` - Role status (0=inactive, 1=active) - accepts multiple values
- `searchKey` - Search in role name field
- `offset` - Pagination offset
- `limit` - Number of results per page
- `sortBy[column]` - Sort column (id, name, created_at, updated_at)
- `sortBy[sort]` - Sort direction (ASC, DESC)

## Response Format

Both endpoints return the same response structure as before:

### Users Response
```json
{
  "count": 25,
  "offset": 0,
  "data": [
    {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>",
      "createdAt": "2024-01-15T10:30:00Z",
      "updatedAt": "2024-01-20T14:45:00Z",
      "createdBy": "admin",
      "updatedBy": "admin",
      "status": 1,
      "roles": ["Admin", "User Manager"],
      "userID": "user-123"
    }
  ]
}
```

### Roles Response
```json
{
  "count": 5,
  "offset": 0,
  "data": [
    {
      "id": 1,
      "name": "Administrator",
      "status": 1,
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-01T00:00:00Z",
      "createdBy": "system",
      "updatedBy": "system"
    }
  ]
}
```

## JavaScript/Frontend Examples

### Using Fetch API
```javascript
// Get active users with pagination
const getActiveUsers = async () => {
  const params = new URLSearchParams({
    'offset': '0',
    'limit': '20',
    'status': '1',
    'sortBy[column]': 'name',
    'sortBy[sort]': 'ASC'
  });

  const response = await fetch(`/api/v1/users?${params}`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });

  return response.json();
};

// Get users by multiple role IDs
const getUsersByRoles = async (roleIds) => {
  const params = new URLSearchParams();
  params.append('status', '1');
  roleIds.forEach(roleId => params.append('roleId', roleId));

  const response = await fetch(`/api/v1/users?${params}`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });

  return response.json();
};

// Get roles by name search
const searchRoles = async (searchTerm) => {
  const params = new URLSearchParams({
    'searchKey': searchTerm,
    'status': '1'
  });

  const response = await fetch(`/api/v1/roles/list?${params}`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });

  return response.json();
};
```

### Using Axios
```javascript
// Get users with multiple filters
const getUsersWithFilters = async () => {
  const response = await axios.get('/api/v1/users', {
    params: {
      'searchKey': 'admin',
      'offset': 0,
      'limit': 10,
      'status': [1, 0], // Multiple status values
      'roleId': [1, 2], // Multiple role IDs
      'sortBy[column]': 'created_at',
      'sortBy[sort]': 'DESC'
    },
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });

  return response.data;
};

// Get roles with status filter
const getActiveRoles = async () => {
  const response = await axios.get('/api/v1/roles/list', {
    params: {
      'status': 1,
      'sortBy[column]': 'name',
      'sortBy[sort]': 'ASC'
    },
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });

  return response.data;
};
```

## Migration Notes

- The new GET endpoints use flat query parameters instead of nested filter structures
- Only `status` and `roleId` filters are supported for simplified usage
- Query parameters are automatically mapped to the request structure by the framework
- All existing validation rules and business logic remain unchanged
- The deprecated POST endpoints will continue to work for backward compatibility
- Consider updating client applications to use the new GET endpoints for better RESTful compliance

### Migration from POST to GET

**Old POST Request:**
```json
POST /api/v1/users
{
  "searchKey": "admin",
  "offset": 0,
  "limit": 10,
  "filter": [
    {"column": "status", "value": [1]},
    {"column": "role_id", "value": [1, 2]}
  ],
  "sortBy": {"column": "name", "sort": "ASC"}
}
```

**New GET Request:**
```http
GET /api/v1/users?searchKey=admin&offset=0&limit=10&status=1&roleId=1&roleId=2&sortBy[column]=name&sortBy[sort]=ASC
```

## Error Responses

Error responses remain the same format:
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid filter parameter"
  }
}
```

Common HTTP status codes:
- `200` - Success
- `400` - Bad Request (invalid parameters)
- `401` - Unauthorized (invalid/missing token)
- `403` - Forbidden (insufficient permissions)
- `500` - Internal Server Error
