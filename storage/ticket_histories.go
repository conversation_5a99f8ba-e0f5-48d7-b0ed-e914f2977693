package storage

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"strings"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

// CreateTicketHistory ...
func CreateTicketHistory(ctx context.Context, tx *sql.Tx, ticketHistory *TicketHistoryDTO) (id int64, err error) {
	var query = `
		INSERT INTO ticket_history (ticket_id, data, created_at, created_by, note, action_name, prev_status_id, next_status_id)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?)
	`

	// marshall the data
	data, err := json.Marshal(ticketHistory.Data)
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	result, err := tx.ExecContext(ctx, query, ticketHistory.TicketID, data, ticketHistory.CreatedAt, ticketHistory.CreatedBy, ticketHistory.Note, ticketHistory.ActionName, ticketHistory.PrevStatusID, ticketHistory.NextStatusID)
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	id, err = result.LastInsertId()
	if err != nil {
		return 0, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return id, nil
}

// GetTicketHistoriesByTicketID ...
func GetTicketHistoriesByTicketID(ctx context.Context, db *sql.DB, ticketID int64) ([]TicketHistoryDTO, error) {
	var query = `
		SELECT th.id, th.ticket_id, th.data, th.created_at, th.created_by, th.note, th.action_name, th.prev_status_id, th.next_status_id, u.name
		FROM ticket_history th
		LEFT JOIN users u ON th.created_by = u.id
		WHERE ticket_id = ?
		ORDER BY id DESC
	`

	// scan multiple rows
	rows, err := db.QueryContext(ctx, query, ticketID)
	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}
	//nolint:errcheck
	defer rows.Close()

	var ticketHistories []TicketHistoryDTO
	for rows.Next() {
		var ticketHistory TicketHistoryDTO
		var data string
		err := rows.Scan(&ticketHistory.ID, &ticketHistory.TicketID, &data, &ticketHistory.CreatedAt, &ticketHistory.CreatedBy, &ticketHistory.Note, &ticketHistory.ActionName, &ticketHistory.PrevStatusID, &ticketHistory.NextStatusID, &ticketHistory.CreatedByName)
		if err != nil {
			return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
		}

		// unmarshall the data
		err = json.Unmarshal([]byte(data), &ticketHistory.Data)
		if err != nil {
			return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
		}

		ticketHistories = append(ticketHistories, ticketHistory)
	}

	return ticketHistories, nil
}

// GetActionedUsersFromTicketHistories retrieves a map of ticket IDs to lists of user names who have taken actions on those tickets.
// It returns a map where the key is the ticket ID and the value is a slice of user names.
func GetActionedUsersFromTicketHistories(ctx context.Context, db *sql.DB, ticketIds []interface{}) (map[int64][]string, error) {
	if len(ticketIds) == 0 {
		return make(map[int64][]string), nil
	}

	// Build the IN clause placeholders
	placeholders := utils.CreatePlaceholders(len(ticketIds))

	query := fmt.Sprintf(`
        SELECT th.ticket_id, u.name 
        FROM ticket_history th 
        LEFT JOIN users u ON u.id = th.created_by
        WHERE th.ticket_id IN (%s)
    `, strings.Join(placeholders, ","))

	rows, err := db.QueryContext(ctx, query, ticketIds...)
	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, fmt.Sprintf("failed to query ticket histories: %v", err))
	}
	defer func() {
		if closeErr := rows.Close(); closeErr != nil {
			log.Printf("failed to close rows: %v", closeErr)
		}
	}()

	// Use a map to track unique users per ticket
	uniqueUsers := make(map[int64]map[string]struct{})
	for rows.Next() {
		var ticketId int64
		var userName sql.NullString
		if err := rows.Scan(&ticketId, &userName); err != nil {
			return nil, errorwrapper.Error(apiError.InternalServerError, fmt.Sprintf("failed to scan row: %v", err))
		}

		if _, exists := uniqueUsers[ticketId]; !exists {
			uniqueUsers[ticketId] = make(map[string]struct{})
		}
		if userName.Valid {
			uniqueUsers[ticketId][userName.String] = struct{}{}
		}
	}

	// Convert the unique users map to the final result format
	result := make(map[int64][]string, 0)
	for ticketId, users := range uniqueUsers {
		userList := make([]string, 0)
		for userName := range users {
			userList = append(userList, userName)
		}
		result[ticketId] = userList
	}

	return result, nil
}
