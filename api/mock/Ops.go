// Code generated by mockery v2.44.1. DO NOT EDIT.

package mocks

import (
	context "context"

	api "gitlab.super-id.net/bersama/opsce/onedash-be/api"

	mock "github.com/stretchr/testify/mock"
)

// Ops is an autogenerated mock type for the Ops type
type Ops struct {
	mock.Mock
}

// ExecuteFile provides a mock function with given fields: ctx, req
func (_m *Ops) ExecuteFile(ctx context.Context, req *api.ExecuteFileRequest) (*api.ExecuteFileResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for ExecuteFile")
	}

	var r0 *api.ExecuteFileResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.ExecuteFileRequest) (*api.ExecuteFileResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.ExecuteFileRequest) *api.ExecuteFileResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.ExecuteFileResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.ExecuteFileRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ExecuteFileV2 provides a mock function with given fields: ctx, req
func (_m *Ops) ExecuteFileV2(ctx context.Context, req *api.ExecuteFileRequest) (*api.ExecuteFileResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for ExecuteFileV2")
	}

	var r0 *api.ExecuteFileResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.ExecuteFileRequest) (*api.ExecuteFileResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.ExecuteFileRequest) *api.ExecuteFileResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.ExecuteFileResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.ExecuteFileRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetStatusDocumentV2 provides a mock function with given fields: ctx, req
func (_m *Ops) GetStatusDocumentV2(ctx context.Context, req *api.GetStatusRequest) (*api.GetStatusResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetStatusDocumentV2")
	}

	var r0 *api.GetStatusResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetStatusRequest) (*api.GetStatusResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetStatusRequest) *api.GetStatusResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetStatusResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetStatusRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ValidateFile provides a mock function with given fields: ctx, req
func (_m *Ops) ValidateFile(ctx context.Context, req *api.ValidateFileRequest) (*api.ValidateFileResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for ValidateFile")
	}

	var r0 *api.ValidateFileResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.ValidateFileRequest) (*api.ValidateFileResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.ValidateFileRequest) *api.ValidateFileResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.ValidateFileResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.ValidateFileRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ValidateFileV2 provides a mock function with given fields: ctx, req
func (_m *Ops) ValidateFileV2(ctx context.Context, req *api.ValidateFileRequest) (*api.ValidateFileResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for ValidateFileV2")
	}

	var r0 *api.ValidateFileResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.ValidateFileRequest) (*api.ValidateFileResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.ValidateFileRequest) *api.ValidateFileResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.ValidateFileResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.ValidateFileRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewOps creates a new instance of Ops. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewOps(t interface {
	mock.TestingT
	Cleanup(func())
}) *Ops {
	mock := &Ops{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
