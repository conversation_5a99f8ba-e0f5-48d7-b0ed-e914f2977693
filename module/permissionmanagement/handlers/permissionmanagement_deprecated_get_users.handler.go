package handlers

import (
	"context"

	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
)

// DeprecatedGetUsers handles the deprecated POST /api/v1/users endpoint for backward compatibility
// This method delegates to the main GetUsers handler to avoid code duplication
func (p *PermissionManagementService) DeprecatedGetUsers(ctx context.Context, req *api.GetUsersRequest) (*api.GetUsersResponse, error) {
	// Delegate to the main GetUsers handler to maintain consistency
	return p.GetUsers(ctx, req)
}
