// Code generated by protoc-gen-go-svc. DO NOT EDIT.
// source: permissionmanagement.proto
package handlers

import (
	context "context"
	api "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
	v2 "gitlab.myteksi.net/dakota/servus/v2"
)

// RegisterRoutes registers handlers with the Servus library.
func (p *PermissionManagementService) RegisterRoutes(app *v2.Application) {
	app.POST(
		"/api/v1/user",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.CreateUser(ctx, req.(*api.CreateUserRequest))
			return res, err
		},
		v2.WithRequest(&api.CreateUserRequest{}),
		v2.WithResponse(&api.CreateUserResponse{}),
	)
	app.POST(
		"/api/v1/login",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.<PERSON>gin(ctx, req.(*api.LoginRequest))
			return res, err
		},
		v2.WithRequest(&api.LoginRequest{}),
		v2.WithResponse(&api.LoginResponse{}),
	)
	app.POST(
		"/api/v1/refresh-login",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.RefreshLogin(ctx, req.(*api.RefreshLoginRequest))
			return res, err
		},
		v2.WithRequest(&api.RefreshLoginRequest{}),
		v2.WithResponse(&api.RefreshLoginResponse{}),
	)
	app.POST(
		"/api/v1/logout",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.Logout(ctx)
			return res, err
		},
		v2.WithResponse(&api.LogoutResponse{}),
	)
	app.POST(
		"/api/v1/reset-password",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.ResetPassword(ctx, req.(*api.ResetPasswordRequest))
			return res, err
		},
		v2.WithRequest(&api.ResetPasswordRequest{}),
		v2.WithResponse(&api.ResetPasswordResponse{}),
	)
	app.GET(
		"/api/v1/users",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.GetUsersQuery(ctx, req.(*api.GetUsersQueryRequest))
			return res, err
		},
		v2.WithRequest(&api.GetUsersQueryRequest{}),
		v2.WithResponse(&api.GetUsersResponse{}),
		v2.WithDescription("GetUsersQuery - New GET endpoint for retrieving users with flat query parameters"),
	)
	app.GET(
		"/api/v1/roles/list",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.GetListRoleQuery(ctx, req.(*api.GetListRoleQueryRequest))
			return res, err
		},
		v2.WithRequest(&api.GetListRoleQueryRequest{}),
		v2.WithResponse(&api.GetListRoleResponse{}),
		v2.WithDescription("GetListRoleQuery - New GET endpoint for retrieving roles with flat query parameters"),
	)
	app.POST(
		"/api/v1/users",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.GetUsers(ctx, req.(*api.GetUsersRequest))
			return res, err
		},
		v2.WithRequest(&api.GetUsersRequest{}),
		v2.WithResponse(&api.GetUsersResponse{}),
		v2.WithDescription("GetUsers - [Deprecated] Original POST endpoint for backward compatibility"),
	)
	app.POST(
		"/api/v1/roles/list",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.GetListRole(ctx, req.(*api.GetListRoleRequest))
			return res, err
		},
		v2.WithRequest(&api.GetListRoleRequest{}),
		v2.WithResponse(&api.GetListRoleResponse{}),
		v2.WithDescription("GetListRole - [Deprecated] Original POST endpoint for backward compatibility"),
	)
	app.POST(
		"/api/v1/roles",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.CreateRole(ctx, req.(*api.CreateRoleRequest))
			return res, err
		},
		v2.WithRequest(&api.CreateRoleRequest{}),
		v2.WithResponse(&api.CreateRoleResponse{}),
	)
	app.PUT(
		"/api/v1/roles",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.UpdateRole(ctx, req.(*api.UpdateRoleRequest))
			return res, err
		},
		v2.WithRequest(&api.UpdateRoleRequest{}),
		v2.WithResponse(&api.UpdateRoleResponse{}),
	)
	app.POST(
		"/api/v1/permission",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.CreatePermission(ctx, req.(*api.CreatePermissionRequest))
			return res, err
		},
		v2.WithRequest(&api.CreatePermissionRequest{}),
		v2.WithResponse(&api.CreatePermissionResponse{}),
	)
	app.PUT(
		"/api/v1/permission",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.UpdatePermission(ctx, req.(*api.UpdatePermissionRequest))
			return res, err
		},
		v2.WithRequest(&api.UpdatePermissionRequest{}),
		v2.WithResponse(&api.UpdatePermissionResponse{}),
	)
	app.GET(
		"/api/v1/users/permission",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.GetUserPermissions(ctx)
			return res, err
		},
		v2.WithResponse(&api.GetUserPermissionsResponse{}),
		v2.WithDescription("GetUserPermissions is API to get user detail and permission"),
	)
	app.POST(
		"/api/v1/user/status",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.UpdateUserStatus(ctx, req.(*api.UpdateUserStatusRequest))
			return res, err
		},
		v2.WithRequest(&api.UpdateUserStatusRequest{}),
		v2.WithResponse(&api.UpdateUserStatusResponse{}),
		v2.WithDescription("UpdateUserStatus: API to update user status"),
	)
	app.PUT(
		"/api/v1/user",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.UpdateUser(ctx, req.(*api.UpdateUserRequest))
			return res, err
		},
		v2.WithRequest(&api.UpdateUserRequest{}),
		v2.WithResponse(&api.UpdateUserResponse{}),
		v2.WithDescription("UpdateUser: API to update user"),
	)
	app.POST(
		"/api/v1/roles/status",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.UpdateRoleStatus(ctx, req.(*api.UpdateRoleStatusRequest))
			return res, err
		},
		v2.WithRequest(&api.UpdateRoleStatusRequest{}),
		v2.WithResponse(&api.UpdateRoleStatusResponse{}),
		v2.WithDescription("UpdateRoleStatus is API for deactivate and reactivate role"),
	)
	app.GET(
		"/api/v1/roles/detail/:id",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.GetRoleDetail(ctx, req.(*api.GetRoleDetailRequest))
			return res, err
		},
		v2.WithRequest(&api.GetRoleDetailRequest{}),
		v2.WithResponse(&api.GetRoleDetailResponse{}),
		v2.WithDescription("GetRoleDetail is API for get role details by id"),
	)
	app.POST(
		"/api/v1/permissions",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.GetPermissionList(ctx, req.(*api.GetPermissionListRequest))
			return res, err
		},
		v2.WithRequest(&api.GetPermissionListRequest{}),
		v2.WithResponse(&api.GetPermissionListResponse{}),
		v2.WithDescription("GetPermissionList is API for get permission list in admin config"),
	)
	app.POST(
		"/api/v1/permission/status",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.UpdatePermissionStatus(ctx, req.(*api.UpdatePermissionStatusRequest))
			return res, err
		},
		v2.WithRequest(&api.UpdatePermissionStatusRequest{}),
		v2.WithResponse(&api.UpdatePermissionStatusResponse{}),
		v2.WithDescription("UpdatePermissionStatus is API for update permission in admin config"),
	)
	app.POST(
		"/api/v1/auth",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.Authenticate(ctx, req.(*api.AuthenticateRequest))
			return res, err
		},
		v2.WithRequest(&api.AuthenticateRequest{}),
		v2.WithResponse(&api.AuthenticateResponse{}),
		v2.WithDescription("Authenticate is internal endpoint used for other backend services to authenticate token from onedash-fe"),
	)
}
