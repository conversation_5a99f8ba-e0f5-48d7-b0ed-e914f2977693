package handlers

import (
	"context"

	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
)

// DeprecatedGetListRole handles the deprecated POST /api/v1/roles/list endpoint for backward compatibility
// This method delegates to the main GetListRole handler to avoid code duplication
func (p *PermissionManagementService) DeprecatedGetListRole(ctx context.Context, req *api.GetListRoleRequest) (*api.GetListRoleResponse, error) {
	// Delegate to the main GetListRole handler to maintain consistency
	return p.GetListRole(ctx, req)
}
