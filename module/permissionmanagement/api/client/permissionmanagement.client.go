// Code generated by protoc-gen-go-svc. DO NOT EDIT.
// source: permissionmanagement.proto
package client

import (
	bytes "bytes"
	context "context"
	fmt "fmt"
	_go "github.com/json-iterator/go"
	api "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
	klient "gitlab.myteksi.net/dakota/klient"
	errorhandling "gitlab.myteksi.net/dakota/klient/errorhandling"
	initialize "gitlab.myteksi.net/dakota/klient/initialize"
	http "net/http"
)

// PermissionManagementClient makes calls to PermissionManagement service.
type PermissionManagementClient struct {
	machinery klient.RoundTripper
}

// MakePermissionManagementClient instantiates a new PermissionManagementClient.
// Deprecated: Use NewPermissionManagementClient instead
func MakePermissionManagementClient(initializer klient.Initializer) (*PermissionManagementClient, error) {
	roundTripper, err := initializer.Initialize()
	if err != nil {
		return nil, err
	}
	return &PermissionManagementClient{
		machinery: roundTripper,
	}, nil
}

// NewPermissionManagementClient instantiates a new PermissionManagementClient.
func NewPermissionManagementClient(baseURL string, options ...klient.Option) (*PermissionManagementClient, error) {
	initializer := initialize.New(baseURL, options...)
	roundTripper, err := initializer.Initialize()
	if err != nil {
		return nil, err
	}
	return &PermissionManagementClient{
		machinery: roundTripper,
	}, nil
}

func (p *PermissionManagementClient) CreateUser(ctx context.Context, req *api.CreateUserRequest) (*api.CreateUserResponse, error) {
	reqShell := (*CreateUserRequestShell)(req)
	resShell := &CreateUserResponseShell{}
	clientCtx := klient.MakeContext(ctx, &createUserDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.CreateUserResponse)(resShell), err
}

func (p *PermissionManagementClient) Login(ctx context.Context, req *api.LoginRequest) (*api.LoginResponse, error) {
	reqShell := (*LoginRequestShell)(req)
	resShell := &LoginResponseShell{}
	clientCtx := klient.MakeContext(ctx, &loginDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.LoginResponse)(resShell), err
}

func (p *PermissionManagementClient) RefreshLogin(ctx context.Context, req *api.RefreshLoginRequest) (*api.RefreshLoginResponse, error) {
	reqShell := (*RefreshLoginRequestShell)(req)
	resShell := &RefreshLoginResponseShell{}
	clientCtx := klient.MakeContext(ctx, &refreshLoginDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.RefreshLoginResponse)(resShell), err
}

func (p *PermissionManagementClient) Logout(ctx context.Context) (*api.LogoutResponse, error) {
	reqShell := (*LogoutRequestShell)(&struct{}{})
	resShell := &LogoutResponseShell{}
	clientCtx := klient.MakeContext(ctx, &logoutDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.LogoutResponse)(resShell), err
}

func (p *PermissionManagementClient) ResetPassword(ctx context.Context, req *api.ResetPasswordRequest) (*api.ResetPasswordResponse, error) {
	reqShell := (*ResetPasswordRequestShell)(req)
	resShell := &ResetPasswordResponseShell{}
	clientCtx := klient.MakeContext(ctx, &resetPasswordDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.ResetPasswordResponse)(resShell), err
}

// GetUsers - New GET endpoint for retrieving users with query parameters
func (p *PermissionManagementClient) GetUsers(ctx context.Context, req *api.GetUsersRequest) (*api.GetUsersResponse, error) {
	reqShell := (*GetUsersRequestShell)(req)
	resShell := &GetUsersResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getUsersDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetUsersResponse)(resShell), err
}

// GetListRole - New GET endpoint for retrieving roles with query parameters
func (p *PermissionManagementClient) GetListRole(ctx context.Context, req *api.GetListRoleRequest) (*api.GetListRoleResponse, error) {
	reqShell := (*GetListRoleRequestShell)(req)
	resShell := &GetListRoleResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getListRoleDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetListRoleResponse)(resShell), err
}

// DeprecatedGetUsers - Deprecated POST endpoint for backward compatibility
func (p *PermissionManagementClient) DeprecatedGetUsers(ctx context.Context, req *api.GetUsersRequest) (*api.GetUsersResponse, error) {
	reqShell := (*DeprecatedGetUsersRequestShell)(req)
	resShell := &DeprecatedGetUsersResponseShell{}
	clientCtx := klient.MakeContext(ctx, &deprecatedGetUsersDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetUsersResponse)(resShell), err
}

// DeprecatedGetListRole - Deprecated POST endpoint for backward compatibility
func (p *PermissionManagementClient) DeprecatedGetListRole(ctx context.Context, req *api.GetListRoleRequest) (*api.GetListRoleResponse, error) {
	reqShell := (*DeprecatedGetListRoleRequestShell)(req)
	resShell := &DeprecatedGetListRoleResponseShell{}
	clientCtx := klient.MakeContext(ctx, &deprecatedGetListRoleDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetListRoleResponse)(resShell), err
}

func (p *PermissionManagementClient) CreateRole(ctx context.Context, req *api.CreateRoleRequest) (*api.CreateRoleResponse, error) {
	reqShell := (*CreateRoleRequestShell)(req)
	resShell := &CreateRoleResponseShell{}
	clientCtx := klient.MakeContext(ctx, &createRoleDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.CreateRoleResponse)(resShell), err
}

func (p *PermissionManagementClient) UpdateRole(ctx context.Context, req *api.UpdateRoleRequest) (*api.UpdateRoleResponse, error) {
	reqShell := (*UpdateRoleRequestShell)(req)
	resShell := &UpdateRoleResponseShell{}
	clientCtx := klient.MakeContext(ctx, &updateRoleDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.UpdateRoleResponse)(resShell), err
}

func (p *PermissionManagementClient) CreatePermission(ctx context.Context, req *api.CreatePermissionRequest) (*api.CreatePermissionResponse, error) {
	reqShell := (*CreatePermissionRequestShell)(req)
	resShell := &CreatePermissionResponseShell{}
	clientCtx := klient.MakeContext(ctx, &createPermissionDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.CreatePermissionResponse)(resShell), err
}

func (p *PermissionManagementClient) UpdatePermission(ctx context.Context, req *api.UpdatePermissionRequest) (*api.UpdatePermissionResponse, error) {
	reqShell := (*UpdatePermissionRequestShell)(req)
	resShell := &UpdatePermissionResponseShell{}
	clientCtx := klient.MakeContext(ctx, &updatePermissionDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.UpdatePermissionResponse)(resShell), err
}

// GetUserPermissions is API to get user detail and permission
func (p *PermissionManagementClient) GetUserPermissions(ctx context.Context) (*api.GetUserPermissionsResponse, error) {
	reqShell := (*GetUserPermissionsRequestShell)(&struct{}{})
	resShell := &GetUserPermissionsResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getUserPermissionsDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetUserPermissionsResponse)(resShell), err
}

// UpdateUserStatus: API to update user status
func (p *PermissionManagementClient) UpdateUserStatus(ctx context.Context, req *api.UpdateUserStatusRequest) (*api.UpdateUserStatusResponse, error) {
	reqShell := (*UpdateUserStatusRequestShell)(req)
	resShell := &UpdateUserStatusResponseShell{}
	clientCtx := klient.MakeContext(ctx, &updateUserStatusDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.UpdateUserStatusResponse)(resShell), err
}

// UpdateUser: API to update user
func (p *PermissionManagementClient) UpdateUser(ctx context.Context, req *api.UpdateUserRequest) (*api.UpdateUserResponse, error) {
	reqShell := (*UpdateUserRequestShell)(req)
	resShell := &UpdateUserResponseShell{}
	clientCtx := klient.MakeContext(ctx, &updateUserDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.UpdateUserResponse)(resShell), err
}

// UpdateRoleStatus is API for deactivate and reactivate role
func (p *PermissionManagementClient) UpdateRoleStatus(ctx context.Context, req *api.UpdateRoleStatusRequest) (*api.UpdateRoleStatusResponse, error) {
	reqShell := (*UpdateRoleStatusRequestShell)(req)
	resShell := &UpdateRoleStatusResponseShell{}
	clientCtx := klient.MakeContext(ctx, &updateRoleStatusDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.UpdateRoleStatusResponse)(resShell), err
}

// GetRoleDetail is API for get role details by id
func (p *PermissionManagementClient) GetRoleDetail(ctx context.Context, req *api.GetRoleDetailRequest) (*api.GetRoleDetailResponse, error) {
	reqShell := (*GetRoleDetailRequestShell)(req)
	resShell := &GetRoleDetailResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getRoleDetailDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetRoleDetailResponse)(resShell), err
}

// GetPermissionList is API for get permission list in admin config
func (p *PermissionManagementClient) GetPermissionList(ctx context.Context, req *api.GetPermissionListRequest) (*api.GetPermissionListResponse, error) {
	reqShell := (*GetPermissionListRequestShell)(req)
	resShell := &GetPermissionListResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getPermissionListDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetPermissionListResponse)(resShell), err
}

// UpdatePermissionStatus is API for update permission in admin config
func (p *PermissionManagementClient) UpdatePermissionStatus(ctx context.Context, req *api.UpdatePermissionStatusRequest) (*api.UpdatePermissionStatusResponse, error) {
	reqShell := (*UpdatePermissionStatusRequestShell)(req)
	resShell := &UpdatePermissionStatusResponseShell{}
	clientCtx := klient.MakeContext(ctx, &updatePermissionStatusDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.UpdatePermissionStatusResponse)(resShell), err
}

// Authenticate is internal endpoint used for other backend services to authenticate token from onedash-fe
func (p *PermissionManagementClient) Authenticate(ctx context.Context, req *api.AuthenticateRequest) (*api.AuthenticateResponse, error) {
	reqShell := (*AuthenticateRequestShell)(req)
	resShell := &AuthenticateResponseShell{}
	clientCtx := klient.MakeContext(ctx, &authenticateDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.AuthenticateResponse)(resShell), err
}

// CreateUserRequestShell is a wrapper to make the object a klient.Request
type CreateUserRequestShell api.CreateUserRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (c *CreateUserRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/user"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(c)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// CreateUserResponseShell is a wrapper to make the object a klient.Request
type CreateUserResponseShell api.CreateUserResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *CreateUserResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(c)
}

// LoginRequestShell is a wrapper to make the object a klient.Request
type LoginRequestShell api.LoginRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (l *LoginRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/login"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(l)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// LoginResponseShell is a wrapper to make the object a klient.Request
type LoginResponseShell api.LoginResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (l *LoginResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(l)
}

// RefreshLoginRequestShell is a wrapper to make the object a klient.Request
type RefreshLoginRequestShell api.RefreshLoginRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (r *RefreshLoginRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/refresh-login"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(r)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// RefreshLoginResponseShell is a wrapper to make the object a klient.Request
type RefreshLoginResponseShell api.RefreshLoginResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (r *RefreshLoginResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(r)
}

// LogoutRequestShell is a wrapper to make the object a klient.Request
type LogoutRequestShell struct{}

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (l *LogoutRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/logout"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(l)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// LogoutResponseShell is a wrapper to make the object a klient.Request
type LogoutResponseShell api.LogoutResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (l *LogoutResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(l)
}

// ResetPasswordRequestShell is a wrapper to make the object a klient.Request
type ResetPasswordRequestShell api.ResetPasswordRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (r *ResetPasswordRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/reset-password"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(r)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// ResetPasswordResponseShell is a wrapper to make the object a klient.Request
type ResetPasswordResponseShell api.ResetPasswordResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (r *ResetPasswordResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(r)
}

// GetUsersRequestShell is a wrapper to make the object a klient.Request
type GetUsersRequestShell api.GetUsersRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetUsersRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/users"
	fullURL := baseURL + filledPath

	return http.NewRequest("GET", fullURL, nil)
}

// GetUsersResponseShell is a wrapper to make the object a klient.Request
type GetUsersResponseShell api.GetUsersResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetUsersResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// GetListRoleRequestShell is a wrapper to make the object a klient.Request
type GetListRoleRequestShell api.GetListRoleRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetListRoleRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/roles/list"
	fullURL := baseURL + filledPath

	return http.NewRequest("GET", fullURL, nil)
}

// GetListRoleResponseShell is a wrapper to make the object a klient.Request
type GetListRoleResponseShell api.GetListRoleResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetListRoleResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// DeprecatedGetUsersRequestShell is a wrapper to make the object a klient.Request
type DeprecatedGetUsersRequestShell api.GetUsersRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (d *DeprecatedGetUsersRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/users"
	fullURL := baseURL + filledPath

	return http.NewRequest("POST", fullURL, nil)
}

// DeprecatedGetUsersResponseShell is a wrapper to make the object a klient.Request
type DeprecatedGetUsersResponseShell api.GetUsersResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (d *DeprecatedGetUsersResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(d)
}

// DeprecatedGetListRoleRequestShell is a wrapper to make the object a klient.Request
type DeprecatedGetListRoleRequestShell api.GetListRoleRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (d *DeprecatedGetListRoleRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/roles/list"
	fullURL := baseURL + filledPath

	return http.NewRequest("POST", fullURL, nil)
}

// DeprecatedGetListRoleResponseShell is a wrapper to make the object a klient.Request
type DeprecatedGetListRoleResponseShell api.GetListRoleResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (d *DeprecatedGetListRoleResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(d)
}

// CreateRoleRequestShell is a wrapper to make the object a klient.Request
type CreateRoleRequestShell api.CreateRoleRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (c *CreateRoleRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/roles"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(c)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// CreateRoleResponseShell is a wrapper to make the object a klient.Request
type CreateRoleResponseShell api.CreateRoleResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *CreateRoleResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(c)
}

// UpdateRoleRequestShell is a wrapper to make the object a klient.Request
type UpdateRoleRequestShell api.UpdateRoleRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (u *UpdateRoleRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/roles"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(u)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("PUT", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// UpdateRoleResponseShell is a wrapper to make the object a klient.Request
type UpdateRoleResponseShell api.UpdateRoleResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (u *UpdateRoleResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(u)
}

// CreatePermissionRequestShell is a wrapper to make the object a klient.Request
type CreatePermissionRequestShell api.CreatePermissionRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (c *CreatePermissionRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/permission"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(c)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// CreatePermissionResponseShell is a wrapper to make the object a klient.Request
type CreatePermissionResponseShell api.CreatePermissionResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *CreatePermissionResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(c)
}

// UpdatePermissionRequestShell is a wrapper to make the object a klient.Request
type UpdatePermissionRequestShell api.UpdatePermissionRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (u *UpdatePermissionRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/permission"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(u)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("PUT", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// UpdatePermissionResponseShell is a wrapper to make the object a klient.Request
type UpdatePermissionResponseShell api.UpdatePermissionResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (u *UpdatePermissionResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(u)
}

// GetUserPermissionsRequestShell is a wrapper to make the object a klient.Request
type GetUserPermissionsRequestShell struct{}

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetUserPermissionsRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/users/permission"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetUserPermissionsResponseShell is a wrapper to make the object a klient.Request
type GetUserPermissionsResponseShell api.GetUserPermissionsResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetUserPermissionsResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// UpdateUserStatusRequestShell is a wrapper to make the object a klient.Request
type UpdateUserStatusRequestShell api.UpdateUserStatusRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (u *UpdateUserStatusRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/user/status"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(u)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// UpdateUserStatusResponseShell is a wrapper to make the object a klient.Request
type UpdateUserStatusResponseShell api.UpdateUserStatusResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (u *UpdateUserStatusResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(u)
}

// UpdateUserRequestShell is a wrapper to make the object a klient.Request
type UpdateUserRequestShell api.UpdateUserRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (u *UpdateUserRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/user"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(u)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("PUT", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// UpdateUserResponseShell is a wrapper to make the object a klient.Request
type UpdateUserResponseShell api.UpdateUserResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (u *UpdateUserResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(u)
}

// UpdateRoleStatusRequestShell is a wrapper to make the object a klient.Request
type UpdateRoleStatusRequestShell api.UpdateRoleStatusRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (u *UpdateRoleStatusRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/roles/status"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(u)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// UpdateRoleStatusResponseShell is a wrapper to make the object a klient.Request
type UpdateRoleStatusResponseShell api.UpdateRoleStatusResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (u *UpdateRoleStatusResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(u)
}

// GetRoleDetailRequestShell is a wrapper to make the object a klient.Request
type GetRoleDetailRequestShell api.GetRoleDetailRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetRoleDetailRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/roles/detail/" + fmt.Sprint(g.Id)
	fullURL := baseURL + filledPath

	pathVar0 := g.Id
	g.Id = 0

	defer func() {
		g.Id = pathVar0
	}()

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetRoleDetailResponseShell is a wrapper to make the object a klient.Request
type GetRoleDetailResponseShell api.GetRoleDetailResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetRoleDetailResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// GetPermissionListRequestShell is a wrapper to make the object a klient.Request
type GetPermissionListRequestShell api.GetPermissionListRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetPermissionListRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/permissions"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetPermissionListResponseShell is a wrapper to make the object a klient.Request
type GetPermissionListResponseShell api.GetPermissionListResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetPermissionListResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// UpdatePermissionStatusRequestShell is a wrapper to make the object a klient.Request
type UpdatePermissionStatusRequestShell api.UpdatePermissionStatusRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (u *UpdatePermissionStatusRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/permission/status"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(u)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// UpdatePermissionStatusResponseShell is a wrapper to make the object a klient.Request
type UpdatePermissionStatusResponseShell api.UpdatePermissionStatusResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (u *UpdatePermissionStatusResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(u)
}

// AuthenticateRequestShell is a wrapper to make the object a klient.Request
type AuthenticateRequestShell api.AuthenticateRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (a *AuthenticateRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/auth"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(a)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// AuthenticateResponseShell is a wrapper to make the object a klient.Request
type AuthenticateResponseShell api.AuthenticateResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (a *AuthenticateResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(a)
}

var createUserDescriptor = klient.EndpointDescriptor{
	Name:        "CreateUser",
	Description: "",
	Method:      "POST",
	Path:        "/api/v1/user",
}

var loginDescriptor = klient.EndpointDescriptor{
	Name:        "Login",
	Description: "",
	Method:      "POST",
	Path:        "/api/v1/login",
}

var refreshLoginDescriptor = klient.EndpointDescriptor{
	Name:        "RefreshLogin",
	Description: "",
	Method:      "POST",
	Path:        "/api/v1/refresh-login",
}

var logoutDescriptor = klient.EndpointDescriptor{
	Name:        "Logout",
	Description: "",
	Method:      "POST",
	Path:        "/api/v1/logout",
}

var resetPasswordDescriptor = klient.EndpointDescriptor{
	Name:        "ResetPassword",
	Description: "",
	Method:      "POST",
	Path:        "/api/v1/reset-password",
}

var getUsersDescriptor = klient.EndpointDescriptor{
	Name:        "GetUsers",
	Description: "GetUsers - New GET endpoint for retrieving users with query parameters",
	Method:      "GET",
	Path:        "/api/v1/users",
}

var getListRoleDescriptor = klient.EndpointDescriptor{
	Name:        "GetListRole",
	Description: "GetListRole - New GET endpoint for retrieving roles with query parameters",
	Method:      "GET",
	Path:        "/api/v1/roles/list",
}

var deprecatedGetUsersDescriptor = klient.EndpointDescriptor{
	Name:        "DeprecatedGetUsers",
	Description: "DeprecatedGetUsers - Deprecated POST endpoint for backward compatibility",
	Method:      "POST",
	Path:        "/api/v1/users",
}

var deprecatedGetListRoleDescriptor = klient.EndpointDescriptor{
	Name:        "DeprecatedGetListRole",
	Description: "DeprecatedGetListRole - Deprecated POST endpoint for backward compatibility",
	Method:      "POST",
	Path:        "/api/v1/roles/list",
}

var createRoleDescriptor = klient.EndpointDescriptor{
	Name:        "CreateRole",
	Description: "",
	Method:      "POST",
	Path:        "/api/v1/roles",
}

var updateRoleDescriptor = klient.EndpointDescriptor{
	Name:        "UpdateRole",
	Description: "",
	Method:      "PUT",
	Path:        "/api/v1/roles",
}

var createPermissionDescriptor = klient.EndpointDescriptor{
	Name:        "CreatePermission",
	Description: "",
	Method:      "POST",
	Path:        "/api/v1/permission",
}

var updatePermissionDescriptor = klient.EndpointDescriptor{
	Name:        "UpdatePermission",
	Description: "",
	Method:      "PUT",
	Path:        "/api/v1/permission",
}

var getUserPermissionsDescriptor = klient.EndpointDescriptor{
	Name:        "GetUserPermissions",
	Description: "GetUserPermissions is API to get user detail and permission",
	Method:      "GET",
	Path:        "/api/v1/users/permission",
}

var updateUserStatusDescriptor = klient.EndpointDescriptor{
	Name:        "UpdateUserStatus",
	Description: "UpdateUserStatus: API to update user status",
	Method:      "POST",
	Path:        "/api/v1/user/status",
}

var updateUserDescriptor = klient.EndpointDescriptor{
	Name:        "UpdateUser",
	Description: "UpdateUser: API to update user",
	Method:      "PUT",
	Path:        "/api/v1/user",
}

var updateRoleStatusDescriptor = klient.EndpointDescriptor{
	Name:        "UpdateRoleStatus",
	Description: "UpdateRoleStatus is API for deactivate and reactivate role",
	Method:      "POST",
	Path:        "/api/v1/roles/status",
}

var getRoleDetailDescriptor = klient.EndpointDescriptor{
	Name:        "GetRoleDetail",
	Description: "GetRoleDetail is API for get role details by id",
	Method:      "GET",
	Path:        "/api/v1/roles/detail/{id}",
}

var getPermissionListDescriptor = klient.EndpointDescriptor{
	Name:        "GetPermissionList",
	Description: "GetPermissionList is API for get permission list in admin config",
	Method:      "POST",
	Path:        "/api/v1/permissions",
}

var updatePermissionStatusDescriptor = klient.EndpointDescriptor{
	Name:        "UpdatePermissionStatus",
	Description: "UpdatePermissionStatus is API for update permission in admin config",
	Method:      "POST",
	Path:        "/api/v1/permission/status",
}

var authenticateDescriptor = klient.EndpointDescriptor{
	Name:        "Authenticate",
	Description: "Authenticate is internal endpoint used for other backend services to authenticate token from onedash-fe",
	Method:      "POST",
	Path:        "/api/v1/auth",
}
