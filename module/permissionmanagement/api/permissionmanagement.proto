syntax = "proto3";

package onedash;

option go_package = "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api";

import "gxs/api/annotations.proto";
import "google/api/annotations.proto";
import "google/protobuf/any.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";


message CreateUserRequest {
  string name = 1 [(gxs.api.validate) = "required"];
  string email = 2 [(gxs.api.validate) = "required,email"];
  int64 status = 3 [(gxs.api.validate) = "binary"];
  repeated int64 roleIDs = 4;
  string password = 5;
}

message CreateUserResponse {
  string status = 1;
}

message UpdateUserRequest {
  string name = 1 [(gxs.api.validate) = "required"];
  string email = 2 [(gxs.api.validate) = "required,email"];
  int64 status = 3 [(gxs.api.validate) = "binary"];
  repeated int64 roleIDs = 4;
  string userID = 5;
}

message UpdateUserResponse {
  string status = 1;
}

enum LoginType {
  NATIVE = 0;
  JUMPCLOUD = 1;
  SERVICE = 2;
}

message LoginRequest {
  string email = 1 [(gxs.api.validate) = "omitempty,email,email_domain"];
  string password = 2;
  LoginType type = 3 [(gxs.api.validate) = "required,oneof=NATIVE JUMPCLOUD SERVICE"];
  string token = 4;
  string clientID = 5;
  string clientSecret = 6;
}

message LoginResponse {
  string token = 1;
  string refreshToken = 2;
}

message RefreshLoginRequest {
  string refreshToken = 1 [(gxs.api.validate) = "required"];
}

message RefreshLoginResponse {
  string status = 1;
  string token = 2;
}

message LogoutRequest {}

message LogoutResponse {
  string status = 1;
}

message ResetPasswordRequest {
  string password = 1 [(gxs.api.validate) = "required"];
  string confirmPassword = 2 [(gxs.api.validate) = "required"];
}

message ResetPasswordResponse {
  string status = 1;
}

message GetUsersRequest {
  string searchKey = 1;
  int64 offset = 2 [(gxs.api.validate) = "offset"];
  int64 limit = 3 [(gxs.api.validate) = "limit"];
  repeated Filter filter = 4 [(gxs.api.validate) = "omitempty,users_filter"];
  Sort sortBy = 5 [(gxs.api.validate) = "omitempty,users_sort"];
}

message GetUsersResponse {
  int64 count = 1;
  int64 offset = 2;
  repeated User data = 3;
}

message User {
  int64 id = 1;
  string name = 2;
  string email = 3;
  string createdAt = 4;
  string updatedAt = 5;
  string createdBy = 6;
  string updatedBy = 7;
  int64 status = 8;
  repeated string roles = 9;
  string userID = 10;
}

message CreateRoleRequest {
  string name = 1  [(gxs.api.validate) = "required"];
  int64 status = 2 [(gxs.api.validate) = "binary"];
  repeated ElementPermissionsRequest elementPermissions = 3;
}

message ElementPermissionsRequest {
  int64 elementID = 1;
  repeated int64 permissionsIDs = 2;
}

message CreateRoleResponse {
  int64 id = 1;
  string name = 2;
  int64 status = 3;
  string createdAt = 4;
  string createdBy = 5;
}

message UpdateRoleRequest {
  int64 id = 1;
  string name = 2  [(gxs.api.validate) = "required"];
  int64 status = 3 [(gxs.api.validate) = "binary"];
  repeated ElementPermissionsRequest elementPermissions = 4;
}

message UpdateRoleResponse {
  int64 id = 1;
  string name = 2;
  int64 status = 3;
  string updatedAt = 4;
  string updatedBy = 5;
}

message CreatePermissionRequest {
  string name = 1  [(gxs.api.validate) = "required"];
  string description = 2;
  int64 bitwise = 3 [(gxs.api.validate) = "required,permission_bitwise"];
  int64 moduleID = 4 [(gxs.api.validate) = "required"];
  int32 status = 5;
}

message CreatePermissionResponse {
  int64 id = 1;
  string status = 2;
}

message UpdatePermissionRequest {
  int64 id = 1;
  string name = 2 [(gxs.api.validate) = "required"];
  string description = 3;
  int64 bitwise = 4 [(gxs.api.validate) = "required,permission_bitwise"];
  int64 moduleID = 5 [(gxs.api.validate) = "required"];
  int32 status = 6;
}

message UpdatePermissionResponse {
  int64 id = 1;
  string status = 2;
}

message GetListRoleRequest {
  string searchKey = 1;
  int64 offset = 2 [(gxs.api.validate) = "offset"];
  int64 limit = 3 [(gxs.api.validate) = "limit"];
  repeated Filter filter = 4 [(gxs.api.validate) = "omitempty,roles_filter"];
  Sort sortBy = 5 [(gxs.api.validate) = "omitempty,roles_sort_by"];
}

message Filter {
  string column = 1;
  repeated google.protobuf.Any value = 2;
}

enum SortOrder {
  ASC = 0;
  DESC = 1;
}

message Sort {
  string column = 1;
  SortOrder sort = 2;
}

message GetListRoleResponse {
  int64 count = 1;
  int64 offset = 2;
  repeated Role data = 3;
}

message Role {
  int64 id = 1;
  string name = 2;
  int64 status = 3;
  string createdAt = 4;
  string updatedAt = 5;
  string createdBy = 6;
  string updatedBy = 7;
}

message GetUserPermissionsRequest {}

message GetUserPermissionsResponse {
  string userId = 1;
  string email = 2;
  string name = 3;
  repeated string modules = 4;
  repeated string roles = 5;
  repeated UserPermission permissions = 6;
}

message UserPermission {
  string elementCode = 1;
  int64 bitwise = 2;
}

message UpdateUserStatusRequest {
  string userID = 1 [(gxs.api.validate) = "required"];
  int64 status = 2;
}

message UpdateUserStatusResponse {
  string status = 1;
}

message UpdateRoleStatusRequest {
  int64 id = 1 [(gxs.api.validate) = "required"];
  int64 status = 2 [(gxs.api.validate) = "binary"];
}

message UpdateRoleStatusResponse {
  int64 id = 1;
  string status = 2;
}

message GetRoleDetailRequest {
  int64 id = 1 [(gxs.api.validate) = "required"];
}

message GetRoleDetailResponse {
  int64 id = 1;
  string name = 2;
  int64 status = 3;
  string createdAt = 4;
  string createdBy = 5;
  string updatedAt = 6;
  string updatedBy = 7;
  repeated RoleDetailModule modules = 8;
}

message RoleDetailModule {
  int64 moduleID = 1;
  string name = 2;
  repeated RoleDetailElement elements = 3;
}

message RoleDetailElement {
  int64 elementID = 1;
  string name = 2;
  repeated RoleDetailPermission permissions = 3;
}

message RoleDetailPermission {
  int64 permissionID = 1;
  string name = 2;
}

message GetPermissionListRequest {
  string searchKey = 1;
  int64 offset = 2 [(gxs.api.validate) = "offset"];
  int64 limit = 3 [(gxs.api.validate) = "limit"];
  repeated Filter filter = 4 [(gxs.api.validate) = "omitempty,permissions_filter"];
  Sort sortBy = 5 [(gxs.api.validate) = "omitempty,permissions_sort_by"];
}

message GetPermissionListResponse {
  int64 count = 1;
  int64 offset = 2;
  repeated Permission data = 3;
}

message Permission {
  int64 id = 1;
  string name = 2;
  string description = 3;
  string createdAt = 4;
  string updatedAt = 5;
  string createdBy = 6;
  string updatedBy = 7;
  int64 status = 8;
  int64 moduleID = 9;
  string moduleName = 10;
  int64 bitwise = 11;
}

message UpdatePermissionStatusRequest {
  int64 id = 1 [(gxs.api.validate) = "required"];
  int32 status = 2 [(gxs.api.validate) = "binary"];
}

message UpdatePermissionStatusResponse {
  string status = 1;
}

// AuthenticateRequest ...
message AuthenticateRequest {
  string token = 1 [(gxs.api.validate) = "required"];
  string elementCode = 2 [(gxs.api.validate) = "required"];
  int64 bitwiseRequired = 3;
}

// AuthenticateResponse ...
message AuthenticateResponse {
  string userID = 1;
  repeated Permission permissions = 2;
}

service PermissionManagement {
  rpc CreateUser(CreateUserRequest) returns (CreateUserResponse) {
    option (google.api.http) = {
      post: "/api/v1/user",
      body: "*",
    };
  }

  rpc Login(LoginRequest) returns (LoginResponse) {
    option (google.api.http) = {
      post: "/api/v1/login",
      body: "*",
    };
  }

  rpc RefreshLogin(RefreshLoginRequest) returns (RefreshLoginResponse) {
    option (google.api.http) = {
      post: "/api/v1/refresh-login",
      body: "*",
    };
  }

  rpc Logout(LogoutRequest) returns (LogoutResponse) {
    option (google.api.http) = {
      post: "/api/v1/logout",
      body: "*",
    };
  }

  rpc ResetPassword(ResetPasswordRequest) returns (ResetPasswordResponse) {
    option (google.api.http) = {
      post: "/api/v1/reset-password",
      body: "*",
    };
  }

  // GetUsers - New GET endpoint for retrieving users with query parameters
  rpc GetUsers(GetUsersRequest) returns (GetUsersResponse) {
    option (google.api.http) = {
      get: "/api/v1/users",
    };
  }

  // GetListRole - New GET endpoint for retrieving roles with query parameters
  rpc GetListRole(GetListRoleRequest) returns (GetListRoleResponse) {
    option (google.api.http) = {
      get: "/api/v1/roles/list",
    };
  }

  // DeprecatedGetUsers - Deprecated POST endpoint for backward compatibility
  rpc DeprecatedGetUsers(GetUsersRequest) returns (GetUsersResponse) {
    option (google.api.http) = {
      post: "/api/v1/users",
    };
    option deprecated = true;
  }

  // DeprecatedGetListRole - Deprecated POST endpoint for backward compatibility
  rpc DeprecatedGetListRole(GetListRoleRequest) returns (GetListRoleResponse) {
    option (google.api.http) = {
      post: "/api/v1/roles/list",
    };
    option deprecated = true;
  }

  rpc CreateRole(CreateRoleRequest) returns (CreateRoleResponse) {
    option (google.api.http) = {
      post: "/api/v1/roles",
      body: "*",
    };
  }

  rpc UpdateRole(UpdateRoleRequest) returns (UpdateRoleResponse) {
    option (google.api.http) = {
      put: "/api/v1/roles",
      body: "*",
    };
  }

  rpc CreatePermission(CreatePermissionRequest) returns (CreatePermissionResponse) {
    option (google.api.http) = {
      post: "/api/v1/permission",
      body: "*",
    };
  }

  rpc UpdatePermission(UpdatePermissionRequest) returns (UpdatePermissionResponse) {
    option (google.api.http) = {
      put: "/api/v1/permission",
      body: "*",
    };
  }


  // GetUserPermissions is API to get user detail and permission
  rpc GetUserPermissions (GetUserPermissionsRequest) returns (GetUserPermissionsResponse){
    option (google.api.http) = {
      get : "/api/v1/users/permission",
      body : "*",
    };
  }

  // UpdateUserStatus: API to update user status
  rpc UpdateUserStatus(UpdateUserStatusRequest) returns (UpdateUserStatusResponse) {
    option (google.api.http) = {
      post: "/api/v1/user/status",
      body: "*",
    };
  }

  // UpdateUser: API to update user
  rpc UpdateUser(UpdateUserRequest) returns (UpdateUserResponse) {
    option (google.api.http) = {
      put: "/api/v1/user",
      body: "*",
    };
  }

  // UpdateRoleStatus is API for deactivate and reactivate role
  rpc UpdateRoleStatus(UpdateRoleStatusRequest) returns (UpdateRoleStatusResponse) {
    option (google.api.http) = {
      post: "/api/v1/roles/status",
      body: "*",
    };
  }

  // GetRoleDetail is API for get role details by id
  rpc GetRoleDetail(GetRoleDetailRequest) returns (GetRoleDetailResponse) {
    option (google.api.http) = {
      get: "/api/v1/roles/detail/{id}",
      body: "*",
    };
  }

  // GetPermissionList is API for get permission list in admin config
  rpc GetPermissionList(GetPermissionListRequest) returns (GetPermissionListResponse) {
    option (google.api.http) = {
      post: "/api/v1/permissions",
      body: "*"
    };
  }

  // UpdatePermissionStatus is API for update permission in admin config
  rpc UpdatePermissionStatus(UpdatePermissionStatusRequest) returns (UpdatePermissionStatusResponse) {
    option (google.api.http) = {
      post: "/api/v1/permission/status",
      body: "*"
    };
  }

  // Authenticate is internal endpoint used for other backend services to authenticate token from onedash-fe
  rpc Authenticate(AuthenticateRequest) returns (AuthenticateResponse) {
    option (google.api.http) = {
      post: "/api/v1/auth",
      body: "*",
    };
  }

}