package logic

import (
	"context"
	"database/sql"
	"fmt"
	"strconv"
	"strings"
	"time"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/validations"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	permissionManagementLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic"
	permissionManagementStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
)

// CreateTicket creates a ticket
//
// nolint: funlen, dupl, errcheck, gocognit
func (p *process) CreateTicket(ctx context.Context, req *api.CreateTicketRequest) (*api.CreateTicketResponse, error) {
	// Validate the request
	err := validations.ValidateRequest(req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.BadRequest, "failed to validate request")
	}

	// attempt to get ticket payload as validation
	// Skip validation for draft tickets
	skipValidation := req.Action == constants.ActionMakerDraft
	req.Data.Payload, err = p.makeTicketPayload(ctx, req.Data.Payload, req.ElementID, skipValidation)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to make ticket payload")
	}

	var user *permissionManagementStorage.UserDTO
	if req.Source == api.TicketSource_ONEDASH {
		// Authenticate the request
		user, _, err = permissionManagementLogic.PermissionManagementProcess.AuthenticateRequestForElement(ctx, req.ElementID, constants.BitwiseValueCreateTicket)
		if err != nil {
			return nil, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to authenticate request")
		}
	} else if req.Source == api.TicketSource_AML_SERVICE {
		// Authenticate the request from external service
		user, err = p.authenticateRequestForTicketAction(ctx, req.ElementID, req.Action, req.StatusID)
		if err != nil {
			return nil, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to authenticate request")
		}
	} else {
		return nil, errorwrapper.Error(apiError.BadRequest, "invalid request source")
	}

	// Get the database master handle
	master, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Master)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}

	var res = &api.CreateTicketResponse{}
	dto, err := makeCreateTicketDTO(ctx, master, req, user.ID)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to make create ticket dto")
	}

	// Validate documents before transaction if any are provided
	if len(req.DocumentIDs) > 0 {
		err = storage.ValidateUnlinkedDocuments(ctx, master, req.DocumentIDs)
		if err != nil {
			return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to validate documents")
		}
	}

	// Begin transaction
	tx, err := master.Begin()
	if err != nil {
		return nil, err
	}
	defer tx.Rollback()

	// Insert the ticket
	ticketID, err := storage.CreateTicket(ctx, tx, dto)
	if err != nil {
		return nil, err
	}

	// Insert the ticket history
	historyID, err := storage.CreateTicketHistory(ctx, tx, &storage.TicketHistoryDTO{
		TicketID:     ticketID,
		CreatedBy:    sql.NullInt64{Int64: user.ID, Valid: true},
		CreatedAt:    sql.NullTime{Time: time.Now(), Valid: true},
		Data:         req.Data,
		ActionName:   req.Action,
		Note:         sql.NullString{},
		NextStatusID: dto.TicketStatusID,
	})
	if err != nil {
		return nil, err
	}

	// Link pre-created documents to the ticket
	if len(req.DocumentIDs) > 0 {
		err = storage.LinkDocumentsToTicket(ctx, tx, ticketID, req.DocumentIDs)
		if err != nil {
			return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to link documents to ticket")
		}
	}

	// commit the transaction
	err = tx.Commit()
	if err != nil {
		return nil, errorwrapper.Error(apiError.InternalServerError, fmt.Sprintf("failed to commit transaction: %v", err))
	}

	res.Id = ticketID

	var title, description string
	if req.Action == constants.ActionMakerDraft {
		title = "Ticket draft created"
		description = fmt.Sprintf("User %s created ticket draft with id %d", user.Name, res.Id)
	} else {
		title = "Ticket created"
		description = fmt.Sprintf("User %s created ticket with id %d", user.Name, res.Id)
	}

	WriteAuditLogForTicket(ctx, master, dto, user.ID, res.Id, strconv.Itoa(int(historyID)), title, description, constants.CaseCreated)

	if req.ParentTicketId != 0 {
		WriteAuditLogForTicket(ctx, master, nil, user.ID, req.ParentTicketId, "", "Child case created", fmt.Sprintf("Case %d created.", res.Id), constants.CaseCreated)
	}

	return res, nil
}

func makeCreateTicketDTO(ctx context.Context, db *sql.DB, req *api.CreateTicketRequest, userID int64) (*storage.TicketDTO, error) {
	chains, err := storage.GetTicketChainByElementIDMap(ctx, db, req.ElementID)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get ticket chain by element id")
	}

	var ticketStatus int64
	assigneeUserID := sql.NullInt64{Int64: 0, Valid: false}
	if req.Source == api.TicketSource_ONEDASH {
		if req.Action == constants.ActionMakerSubmit {
			ticketStatus = chains[constants.ActionMakerSubmit].NextStatusID
		} else if req.Action == constants.ActionMakerDraft {
			ticketStatus = chains[constants.ActionMakerDraft].NextStatusID
			assigneeUserID = sql.NullInt64{Int64: userID, Valid: true}
		} else {
			ticketStatus = chains[req.Action].NextStatusID
		}
	} else {
		ticketStatus = chains[req.Action].NextStatusID
	}

	deadlineTime, err := storage.GetTicketSLAByPriorityID(ctx, db, req.PriorityID)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get ticket sla by priority id")
	}

	customerSegmentID, customerSegmentQueryErr := determineTicketCustomerSegment(ctx, db, req.ElementID, req.DomainID)
	if customerSegmentQueryErr != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get customer segment")
	}

	ticketRequestorID, ticketRequestorQueryErr := determineTicketRequestor(ctx, db, req.ElementID, req.TicketRequestorID)
	if ticketRequestorQueryErr != nil {
		return nil, errorwrapper.WrapError(err, apiError.Idem, "failed to get default ticket requestor")
	}

	return &storage.TicketDTO{
		ElementID:         req.ElementID,
		PriorityID:        req.PriorityID,
		Data:              req.Data,
		TicketStatusID:    ticketStatus,
		Source:            string(req.Source),
		DeadlineTime:      sql.NullTime{Time: deadlineTime, Valid: true},
		CreatedBy:         sql.NullInt64{Int64: userID, Valid: true},
		CreatedAt:         sql.NullTime{Time: time.Now(), Valid: true},
		UpdatedBy:         sql.NullInt64{Int64: userID, Valid: true},
		UpdatedAt:         sql.NullTime{Time: time.Now(), Valid: true},
		AssigneeUserID:    assigneeUserID,
		CaseCategory:      sql.NullString{String: req.CaseCategory, Valid: req.CaseCategory != ""},
		CaseSubcategory:   sql.NullString{String: req.CaseSubcategory, Valid: req.CaseSubcategory != ""},
		CustomerSegmentID: customerSegmentID,
		DomainID:          sql.NullString{String: req.DomainID, Valid: req.DomainID != ""},
		TicketRequestorID: ticketRequestorID,
		Channel:           sql.NullString{String: req.Channel, Valid: req.Channel != ""},
		ParentTicketID:    sql.NullInt64{Int64: req.ParentTicketId, Valid: req.ParentTicketId != 0},
	}, nil
}

// determineTicketCustomerSegment find out customer segment from ticket details
func determineTicketCustomerSegment(ctx context.Context, db *sql.DB, elementID int64, domainID string) (sql.NullInt64, error) {
	// If domainID is provided, determine segment based on prefix
	if domainID != "" {
		segmentID := constants.CustomerSegementRetail
		if strings.HasPrefix(domainID, "BIF") {
			segmentID = constants.CustomerSegementBIZ
		}

		return sql.NullInt64{
			Int64: segmentID,
			Valid: true,
		}, nil
	}

	// If no domainID, get the default segment from the element
	element, err := storage.GetElementByID(ctx, db, elementID)
	if err != nil {
		return sql.NullInt64{Valid: false}, err
	}

	return element.DefaultCustomerSegmentID, nil

}

func determineTicketRequestor(ctx context.Context, db *sql.DB, elementID int64, ticketRequestorID int64) (sql.NullInt64, error) {
	if ticketRequestorID != 0 {
		return sql.NullInt64{Int64: ticketRequestorID, Valid: true}, nil
	}

	element, err := storage.GetElementByID(ctx, db, elementID)
	if err != nil {
		return sql.NullInt64{Valid: false}, err
	}
	return element.DefaultTicketRequestorID, nil
}
